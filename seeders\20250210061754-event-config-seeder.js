"use strict";
const { v4: uuidv4 } = require("uuid");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.bulkInsert('event_config', [
      {
        event_config_id: uuidv4(),
        event_name: 'Email Notification Event',
        event: 'email',
        queue: 'email_queue',
        order: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        event_config_id: uuidv4(),
        event_name: 'Text Notification Event',
        event: 'text',
        queue: 'text_queue',
        order: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        event_config_id: uuidv4(),
        event_name: 'Generate Notification Event',
        event: 'notification',
        queue: 'notification_queue',
        order: 2,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        event_config_id: uuidv4(),
        event_name: 'API 1 Outbound Event',
        event: 'api_1_outbound',
        queue: 'api_1_outbound_queue',
        order: 3,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        event_config_id: uuidv4(),
        event_name: 'API 2 Outbound Event',
        event: 'api_2_outbound',
        queue: 'api_2_outbound_queue',
        order: 3,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        event_config_id: uuidv4(),
        event_name: 'CCURE9000 XML Outbound Event',
        event: 'ccure9000_xml_outbound',
        queue: 'ccure9000_xml_outbound_queue',
        order: 4,
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('event_config', null, {});
  },
};
