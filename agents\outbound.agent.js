// Generic outbound agent that handles both CSV and API processing through handlers
const logger = require('../config/logger');
const PerformanceMonitor = require('../services/performance.service');
const {
  setupQueueConsumer,
  getHandler,
  setupGracefulShutdown,
  validateAgentConfig,
  logAgentActivity
} = require('../helpers/agent.helper');

/**
 * Processes outbound agent based on source type (CSV or API)
 * @param {Object} agent - Agent configuration
 * @returns {Promise<void>}
 */
async function processOutboundAgent(agent) {
  const performanceMonitor = new PerformanceMonitor(`Outbound Processing - ${agent.name}`);

  try {
    logAgentActivity(agent.name, 'Starting outbound processing', {
      type: agent.type,
      source: agent.source,
      handler: agent.handler
    });

    // Validate agent configuration
    if (!validateAgentConfig(agent)) {
      throw new Error(`Invalid agent configuration for ${agent.name}`);
    }

    // Get the appropriate handler
    const handlerFunction = getHandler(agent.handler);

    performanceMonitor.startStep('Handler Execution', {
      agentName: agent.name,
      handlerName: agent.handler,
      source: agent.source
    });

    // Execute handler based on source type
    let result;
    if (agent.source === 'API') {
      // For API agents, we need to set up queue listening
      await setupApiQueueListener(agent, handlerFunction, performanceMonitor);
      return; // API agents run continuously
    } else {
      // For CSV/file-based agents, execute once
      result = await handlerFunction({ agent, performanceMonitor });
    }

    performanceMonitor.endStep('Handler Execution', {
      success: !!result,
      resultType: typeof result
    });

    const finalMetrics = performanceMonitor.complete({
      status: 'success',
      agentName: agent.name,
      handlerName: agent.handler,
      result
    });

    logAgentActivity(agent.name, 'Outbound processing completed successfully', {
      performanceMetrics: finalMetrics.summary
    });

  } catch (error) {
    logger.error(`Error in outbound agent processing for ${agent.name}:`, error.message);

    performanceMonitor.complete({
      status: 'error',
      error: error.message,
      agentName: agent.name
    });

    throw error;
  }
}

/**
 * Sets up queue listener for API outbound agents
 * @param {Object} agent - Agent configuration
 * @param {Function} handlerFunction - Handler function to process messages
 * @param {Object} performanceMonitor - Performance monitor instance
 * @returns {Promise<void>}
 */
async function setupApiQueueListener(agent, handlerFunction, performanceMonitor) {
  try {
    logAgentActivity(agent.name, `Setting up queue listener for: ${agent.queue}`);

    // Shared counters for the session
    let totalMessagesProcessed = 0;
    let successfulMessages = 0;
    let failedMessages = 0;
    let totalApiCalls = 0;
    let validationErrors = [];

    // Message handler function
    const messageHandler = async (msg, channel) => {
      const messageStartTime = Date.now();
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Parse message
      const messageContent = msg.content.toString();
      let event;

      try {
        event = JSON.parse(messageContent);
      } catch (parseError) {
        logger.error(`[${agent.name}] Failed to parse message from queue ${agent.queue}:`, parseError);
        failedMessages++;
        totalMessagesProcessed++;

        // Log parsing error to performance monitor with stack trace
        performanceMonitor.logProgress(`Message parsing failed`, {
          messageId,
          error: parseError,
          totalProcessed: totalMessagesProcessed,
          failed: failedMessages,
          messageContent: messageContent.substring(0, 200) + '...' // First 200 chars for debugging
        });

        channel.ack(msg);
        return;
      }

      logAgentActivity(agent.name, `Processing event from queue ${agent.queue}`, {
        eventType: event.event_type,
        traceId: event.trace_id,
        messageId
      });

      try {
        // Process the event through handler
        const result = await handlerFunction({
          event,
          agent,
          performanceMonitor: performanceMonitor,
          messageId
        });

        // Update counters based on result
        if (result && result.errors && result.errors.length > 0) {
          validationErrors.push(...result.errors);
          failedMessages++;
        } else {
          successfulMessages++;
        }

        if (result && result.apiCalls) {
          totalApiCalls += result.apiCalls;
        }

        totalMessagesProcessed++;

        // Log progress periodically
        if (totalMessagesProcessed % 100 === 0) {
          performanceMonitor.logProgress(`Processed ${totalMessagesProcessed} messages`, {
            successful: successfulMessages,
            failed: failedMessages,
            apiCalls: totalApiCalls,
            validationErrors: validationErrors.length,
            messageProcessingTime: Date.now() - messageStartTime
          });
        }

        logAgentActivity(agent.name, `Successfully processed event`, {
          messageId,
          result: result ? {
            successfulRecords: result.successfulRecords,
            failedRecords: result.failedRecords,
            apiCalls: result.apiCalls
          } : null
        });

        // Acknowledge message after successful processing
        channel.ack(msg);

      } catch (error) {
        logger.error(`[${agent.name}] Error processing message:`, error);
        failedMessages++;
        totalMessagesProcessed++;

        // Log processing error to performance monitor with stack trace
        performanceMonitor.logProgress(`Message processing failed`, {
          messageId,
          error: error,
          totalProcessed: totalMessagesProcessed,
          failed: failedMessages,
          errorType: error.constructor.name
        });

        // Acknowledge message even on error to prevent reprocessing
        channel.ack(msg);
      }
    };

    // Setup queue consumer
    await setupQueueConsumer(agent.queue, messageHandler);

    logAgentActivity(agent.name, `Queue listener established for ${agent.queue}`);

    // Setup graceful shutdown with performance summary
    setupGracefulShutdown(async () => {
      logAgentActivity(agent.name, 'Shutting down API outbound agent');

      // Complete performance monitoring with final summary
      const finalMetrics = await performanceMonitor.complete({
        status: 'shutdown',
        agentName: agent.name,
        totalMessagesProcessed,
        successfulMessages,
        failedMessages,
        totalApiCalls,
        validationErrors: validationErrors.length,
        errorDetails: validationErrors.slice(0, 10) // Include first 10 errors as sample
      });

      logAgentActivity(agent.name, 'Performance summary generated', {
        performanceMetrics: finalMetrics.summary
      });
    });

    // Keep process alive
    logAgentActivity(agent.name, `API outbound agent is now listening for events...`);

    // Set up periodic performance reporting (every 5 minutes)
    const reportingInterval = setInterval(() => {
      if (totalMessagesProcessed > 0) {
        performanceMonitor.logProgress(`Periodic Report`, {
          totalProcessed: totalMessagesProcessed,
          successful: successfulMessages,
          failed: failedMessages,
          successRate: ((successfulMessages / totalMessagesProcessed) * 100).toFixed(2) + '%',
          apiCalls: totalApiCalls,
          validationErrors: validationErrors.length,
          uptime: Math.round((Date.now() - Number(performanceMonitor.startTime) / 1e6) / 1000) + 's'
        });
      }
    }, 5 * 60 * 1000); // 5 minutes

    // Clear interval on shutdown and generate final summary
    const handleShutdown = async (signal) => {
      clearInterval(reportingInterval);

      // Generate final performance summary
      try {
        const finalMetrics = await performanceMonitor.complete({
          status: 'shutdown',
          agentName: agent.name,
          totalMessagesProcessed,
          successfulMessages,
          failedMessages,
          totalApiCalls,
          validationErrors: validationErrors.length,
          errorDetails: validationErrors.slice(0, 10),
          shutdownSignal: signal,
          shutdownTime: new Date().toISOString()
        });

        logAgentActivity(agent.name, 'Final performance summary generated', {
          performanceMetrics: finalMetrics.summary
        });

        // Give a moment for file operations to complete
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        logger.error('Error generating final performance summary:', error);
      }

      process.exit(0);
    };

    process.on('SIGINT', () => handleShutdown('SIGINT'));
    process.on('SIGTERM', () => handleShutdown('SIGTERM'));

    // Handle uncaught exceptions
    process.on('uncaughtException', async (error) => {
      logger.error('Uncaught Exception in outbound agent:', error);
      await handleShutdown('UNCAUGHT_EXCEPTION');
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', async (reason, promise) => {
      logger.error('Unhandled Rejection in outbound agent:', reason);
      await handleShutdown('UNHANDLED_REJECTION');
    });

  } catch (error) {
    logger.error(`[${agent.name}] Failed to setup queue listener for ${agent.queue}:`, error);
    throw error;
  }
}

/**
 * Main outbound agent handler - routes to appropriate processing based on agent configuration
 * @param {Object} agent - Agent configuration
 * @returns {Promise<void>}
 */
async function outboundAgentHandler(agent) {
  try {
    logAgentActivity(agent.name, 'Starting outbound agent handler', {
      type: agent.type,
      source: agent.source,
      queue: agent.queue,
      handler: agent.handler
    });

    await processOutboundAgent(agent);

  } catch (error) {
    logger.error(`Outbound agent handler failed for ${agent.name}:`, error.message);
    throw error;
  }
}

module.exports = { outboundAgentHandler };
